/**
 * TerminalOutputWithModal component - wraps terminal output with modal functionality
 */

import React from 'react';
import { ExternalLink, CheckCircle, XCircle, ChevronDown } from 'lucide-react';
import { SSHInfo } from '../types';
import TerminalModal from './TerminalModal';
import { decodeTerminalOutput } from '../utils/terminalUtils';

interface TerminalOutputWithModalProps {
  sshInfo: SSHInfo;
}

const TerminalOutputWithModal: React.FC<TerminalOutputWithModalProps> = ({ sshInfo }) => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      {/* Original Terminal Output with Arrow Button */}
      <div className={`terminal-output w-full ${sshInfo?.success ? 'terminal-success' : 'terminal-error'} relative group`}>
        {/* Terminal Header */}
        <div className="terminal-header">
          {sshInfo?.success ? <CheckCircle size={12} className="inline"/> : <XCircle size={12} className="inline"/>}
          <span>Command {sshInfo?.success ? 'Succeeded' : 'Failed'}</span>
          <span className="terminal-exit-code ml-auto">(Exit Code: {sshInfo?.exit_status ?? 'N/A'})</span>
        </div>

        {/* Terminal Content */}
        <div className="terminal-content">
          {sshInfo?.stdout && (
            <details className="terminal-details">
              <summary className="terminal-status-success relative group/summary">
                <span>Stdout</span>
                <ChevronDown size={12} className="inline"/>
                {/* Arrow Button - positioned on the same line as Stdout */}
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    openModal();
                  }}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 bg-surface-primary/90 hover:bg-surface-primary border border-theme-primary rounded-sm shadow-sm opacity-0 group-hover:opacity-100 group/summary-hover:opacity-100 transition-all duration-200 hover:scale-105"
                  title="Open in full view"
                  aria-label="Open terminal output in full view"
                >
                  <ExternalLink size={12} className="text-theme-secondary hover:text-theme-primary" />
                </button>
              </summary>
              <pre className="terminal-pre terminal-stdout">{decodeTerminalOutput(sshInfo.stdout)}</pre>
            </details>
          )}
          {sshInfo?.stderr && (
            <details open className="terminal-details">
              <summary className="terminal-status-error relative group/summary">
                <span>Stderr</span>
                <ChevronDown size={12} className="inline"/>
                {/* Arrow Button - positioned on the same line as Stderr */}
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    openModal();
                  }}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 bg-surface-primary/90 hover:bg-surface-primary border border-theme-primary rounded-sm shadow-sm opacity-0 group-hover:opacity-100 group/summary-hover:opacity-100 transition-all duration-200 hover:scale-105"
                  title="Open in full view"
                  aria-label="Open terminal output in full view"
                >
                  <ExternalLink size={12} className="text-theme-secondary hover:text-theme-primary" />
                </button>
              </summary>
              <pre className="terminal-pre terminal-stderr">{decodeTerminalOutput(sshInfo.stderr)}</pre>
            </details>
          )}

          {/* Fallback arrow button when no stdout/stderr */}
          {!sshInfo?.stdout && !sshInfo?.stderr && (
            <div className="relative group/fallback p-2">
              <span className="text-theme-secondary text-sm">No output available</span>
              <button
                onClick={openModal}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 bg-surface-primary/90 hover:bg-surface-primary border border-theme-primary rounded-sm shadow-sm opacity-0 group-hover:opacity-100 group/fallback-hover:opacity-100 transition-all duration-200 hover:scale-105"
                title="Open in full view"
                aria-label="Open terminal output in full view"
              >
                <ExternalLink size={12} className="text-theme-secondary hover:text-theme-primary" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Terminal Modal */}
      <TerminalModal
        isOpen={isModalOpen}
        onClose={closeModal}
        sshInfo={sshInfo}
      />
    </>
  );
};

export default TerminalOutputWithModal;
