/**
 * TerminalModal component for displaying terminal output in full view
 */

import React from 'react';
import { X, Co<PERSON>, CheckCircle, XCircle, ChevronDown } from 'lucide-react';
import { SSHInfo } from '../types';
import { decodeTerminalOutput } from '../utils/terminalUtils';

interface TerminalModalProps {
  isOpen: boolean;
  onClose: () => void;
  sshInfo: SSHInfo;
}

const TerminalModal: React.FC<TerminalModalProps> = ({ isOpen, onClose, sshInfo }) => {
  const [copiedSection, setCopiedSection] = React.useState<string | null>(null);

  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSection(section);
      setTimeout(() => setCopiedSection(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // Handle ESC key press
  React.useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4 bg-black/50 backdrop-blur-sm">
      {/* Modal Container */}
      <div className="relative w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-600 rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-b border-slate-200 dark:border-slate-600 bg-slate-50 dark:bg-slate-800">
          <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
            {sshInfo?.success ? (
              <CheckCircle size={18} className="text-green-500 flex-shrink-0" />
            ) : (
              <XCircle size={18} className="text-red-500 flex-shrink-0" />
            )}
            <h2 className="text-base sm:text-lg font-semibold text-slate-900 dark:text-slate-100 truncate">
              Terminal Output - Command {sshInfo?.success ? 'Succeeded' : 'Failed'}
            </h2>
            <span className="hidden sm:inline text-sm text-slate-600 dark:text-slate-300 bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded flex-shrink-0">
              Exit Code: {sshInfo?.exit_status ?? 'N/A'}
            </span>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 flex-shrink-0"
            aria-label="Close modal"
          >
            <X size={18} />
          </button>
        </div>

        {/* Mobile Exit Code */}
        <div className="sm:hidden px-3 py-2 border-b border-slate-200 dark:border-slate-600 bg-slate-50 dark:bg-slate-800">
          <span className="text-sm text-slate-600 dark:text-slate-300">
            Exit Code: {sshInfo?.exit_status ?? 'N/A'}
          </span>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto max-h-[calc(95vh-120px)] sm:max-h-[calc(90vh-140px)]">
          <div className="p-3 sm:p-4 space-y-4">
            {/* Command Info */}
            {sshInfo?.command && (
              <div className="bg-surface-secondary border border-theme-primary rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-theme-primary">Command:</span>
                  <button
                    onClick={() => copyToClipboard(decodeTerminalOutput(sshInfo.command!), 'command')}
                    className="flex items-center gap-1 text-xs text-theme-secondary hover:text-theme-primary transition-colors px-2 py-1 hover:bg-surface-primary rounded"
                  >
                    {copiedSection === 'command' ? <CheckCircle size={12} /> : <Copy size={12} />}
                    <span className="hidden sm:inline">{copiedSection === 'command' ? 'Copied!' : 'Copy'}</span>
                  </button>
                </div>
                <pre className="terminal-command text-sm overflow-x-auto">{decodeTerminalOutput(sshInfo.command)}</pre>
              </div>
            )}

            {/* Execution Time */}
            {sshInfo?.execution_time && (
              <div className="text-sm text-theme-secondary">
                Execution time: {sshInfo.execution_time}ms
              </div>
            )}

            {/* Stdout Section */}
            {sshInfo?.stdout && (
              <div className="bg-surface-secondary border border-theme-primary rounded-lg overflow-hidden">
                <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border-b border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-2">
                    <CheckCircle size={16} className="text-green-600 dark:text-green-400" />
                    <span className="font-medium text-green-800 dark:text-green-200">Standard Output</span>
                  </div>
                  <button
                    onClick={() => copyToClipboard(decodeTerminalOutput(sshInfo.stdout), 'stdout')}
                    className="flex items-center gap-1 text-xs text-green-700 dark:text-green-300 hover:text-green-900 dark:hover:text-green-100 transition-colors px-2 py-1 hover:bg-green-100 dark:hover:bg-green-800/30 rounded"
                  >
                    {copiedSection === 'stdout' ? <CheckCircle size={12} /> : <Copy size={12} />}
                    <span className="hidden sm:inline">{copiedSection === 'stdout' ? 'Copied!' : 'Copy'}</span>
                  </button>
                </div>
                <div className="p-4 bg-terminal-bg">
                  <pre className="terminal-pre terminal-stdout terminal-modal-output">
                    {decodeTerminalOutput(sshInfo.stdout)}
                  </pre>
                </div>
              </div>
            )}

            {/* Stderr Section */}
            {sshInfo?.stderr && (
              <div className="bg-surface-secondary border border-theme-primary rounded-lg overflow-hidden">
                <div className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
                  <div className="flex items-center gap-2">
                    <XCircle size={16} className="text-red-600 dark:text-red-400" />
                    <span className="font-medium text-red-800 dark:text-red-200">Standard Error</span>
                  </div>
                  <button
                    onClick={() => copyToClipboard(decodeTerminalOutput(sshInfo.stderr), 'stderr')}
                    className="flex items-center gap-1 text-xs text-red-700 dark:text-red-300 hover:text-red-900 dark:hover:text-red-100 transition-colors px-2 py-1 hover:bg-red-100 dark:hover:bg-red-800/30 rounded"
                  >
                    {copiedSection === 'stderr' ? <CheckCircle size={12} /> : <Copy size={12} />}
                    <span className="hidden sm:inline">{copiedSection === 'stderr' ? 'Copied!' : 'Copy'}</span>
                  </button>
                </div>
                <div className="p-4 bg-terminal-bg">
                  <pre className="terminal-pre terminal-stderr terminal-modal-output">
                    {decodeTerminalOutput(sshInfo.stderr)}
                  </pre>
                </div>
              </div>
            )}

            {/* Empty output message */}
            {!sshInfo?.stdout && !sshInfo?.stderr && (
              <div className="text-center py-8 text-theme-secondary">
                <p>No output available</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-t border-theme-primary bg-surface-secondary">
          <div className="text-sm text-theme-secondary hidden sm:block">
            Press <kbd className="px-2 py-1 bg-surface-primary border border-theme-primary rounded text-xs">Esc</kbd> to close
          </div>
          <div className="sm:hidden"></div>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors touch-target-large"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default TerminalModal;
