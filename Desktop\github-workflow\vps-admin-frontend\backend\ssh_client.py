"""
SSH client module for VPS AI Admin Backend.
Handles SSH connections, key management, and command execution.
"""

import asyncio
import traceback
from typing import Dict, Any, Optional
import paramiko

from config import Config
from models import SSHResult


class SSHClient:
    """SSH client for executing commands on remote VPS."""

    def __init__(self, config: Config):
        self.config = config
        self.ssh_key_cache = None
        self.ssh_key_password_cache = config.SSH_KEY_PASSWORD

    async def get_ssh_key(self) -> Optional[paramiko.PKey]:
        """Load and cache SSH private key."""
        if not self.config.SSH_PRIVATE_KEY_PATH:
            return None

        if self.ssh_key_cache:
            return self.ssh_key_cache

        key = None
        local_key_password = self.ssh_key_password_cache

        try:
            print(f"DEBUG: Attempting to load SSH key: {self.config.SSH_PRIVATE_KEY_PATH}")

            # Try different key types
            for key_type in (paramiko.RSAKey, paramiko.Ed25519<PERSON>ey, paramiko.<PERSON>DS<PERSON><PERSON><PERSON>, paramiko.DSSKey):
                try:
                    key = key_type.from_private_key_file(
                        self.config.SSH_PRIVATE_KEY_PATH,
                        password=local_key_password
                    )
                    print(f"INFO: Loaded SSH key type: {type(key).__name__}")
                    self.ssh_key_cache = key
                    self.ssh_key_password_cache = local_key_password
                    return key
                except paramiko.PasswordRequiredException:
                    if local_key_password is None:
                        raise paramiko.AuthenticationException(
                            f"SSH key {self.config.SSH_PRIVATE_KEY_PATH} requires passphrase (SSH_KEY_PASSWORD)."
                        )
                    else:
                        raise paramiko.AuthenticationException("Incorrect SSH key passphrase.")
                except paramiko.SSHException:
                    continue
                except FileNotFoundError:
                    raise FileNotFoundError(f"SSH key file not found: {self.config.SSH_PRIVATE_KEY_PATH}")

            raise paramiko.SSHException(f"Could not load key from {self.config.SSH_PRIVATE_KEY_PATH}")

        except Exception as e:
            print(f"ERROR: Loading SSH key: {type(e).__name__}: {e}")
            self.ssh_key_cache = None
            raise

    async def execute_command_async(self, command: str) -> SSHResult:
        """Execute SSH command asynchronously."""
        loop = asyncio.get_running_loop()
        key = None
        auth_method = "none"

        # Prepare connection parameters
        connect_kwargs = {
            "hostname": self.config.VPS_HOSTNAME,
            "port": self.config.VPS_PORT,
            "username": self.config.VPS_USERNAME,
            "timeout": 20,
            "look_for_keys": False
        }

        # Determine authentication method
        if self.config.SSH_PRIVATE_KEY_PATH:
            try:
                key = await self.get_ssh_key()
                connect_kwargs["pkey"] = key
                connect_kwargs["password"] = None
                auth_method = "key"
                print("DEBUG: SSH Conn: Using Key.")
            except Exception as key_error:
                print(f"WARNING: SSH Key load failed ({type(key_error).__name__}). Trying password for SSH connection.")
                if not self.config.VPS_PASSWORD:
                    return SSHResult(
                        stdout="",
                        stderr=f"SSH Key Error: {key_error} and no VPS_PASSWORD.",
                        exit_status=-1,
                        success=False,
                        command=command
                    )
                connect_kwargs["password"] = self.config.VPS_PASSWORD
                connect_kwargs["pkey"] = None
                auth_method = "password"
        elif self.config.VPS_PASSWORD:
            connect_kwargs["password"] = self.config.VPS_PASSWORD
            connect_kwargs["pkey"] = None
            auth_method = "password"
            print("DEBUG: SSH Conn: Using Password.")
        else:
            return SSHResult(
                stdout="",
                stderr="Internal Auth Error: No valid SSH connection method.",
                exit_status=-1,
                success=False,
                command=command
            )

        def run_ssh():
            """Execute SSH command in thread."""
            import time
            ssh_client = None
            start_time = time.time()

            try:
                ssh_client = paramiko.SSHClient()
                ssh_client.set_missing_host_key_policy(paramiko.WarningPolicy())
                print(f"DEBUG: Connecting SSH ({auth_method})...")
                ssh_client.connect(**connect_kwargs)
                print("INFO: SSH Connection successful.")
                print(f"INFO: Executing command: {command}")

                stdin, stdout, stderr = ssh_client.exec_command(command, timeout=300)

                # Enhanced encoding handling to prevent "?" characters
                def decode_output(data):
                    """Try multiple encoding strategies to properly decode terminal output."""
                    if not data:
                        return ""

                    # Try UTF-8 first (most common)
                    try:
                        return data.decode('utf-8')
                    except UnicodeDecodeError:
                        pass

                    # Try latin-1 (covers all byte values)
                    try:
                        return data.decode('latin-1')
                    except UnicodeDecodeError:
                        pass

                    # Try cp1252 (Windows encoding)
                    try:
                        return data.decode('cp1252')
                    except UnicodeDecodeError:
                        pass

                    # Fallback to UTF-8 with replacement, but preserve more characters
                    return data.decode('utf-8', errors='ignore')

                stdout_raw = stdout.read()
                stderr_raw = stderr.read()
                stdout_data = decode_output(stdout_raw)
                stderr_data = decode_output(stderr_raw)
                exit_status = stdout.channel.recv_exit_status()

                execution_time = int((time.time() - start_time) * 1000)

                print(f"DEBUG: Command exit status: {exit_status}")
                if stdout_data:
                    print(f"DEBUG: Command stdout:\n{stdout_data[:500]}...")
                if stderr_data:
                    print(f"DEBUG: Command stderr:\n{stderr_data[:500]}...")

                return SSHResult(
                    stdout=stdout_data,
                    stderr=stderr_data,
                    exit_status=exit_status,
                    success=exit_status == 0,
                    command=command,
                    execution_time=execution_time
                )

            except paramiko.AuthenticationException as e:
                print(f"ERROR: SSH Auth Failed ({auth_method}): {e}")
                return SSHResult(
                    stdout="",
                    stderr=f"SSH Auth Failed ({auth_method}).",
                    exit_status=-1,
                    success=False,
                    command=command
                )
            except paramiko.SSHException as e:
                print(f"ERROR: SSH Connection Error: {type(e).__name__}: {e}")
                return SSHResult(
                    stdout="",
                    stderr=f"SSH Connection Error: {e}",
                    exit_status=-1,
                    success=False,
                    command=command
                )
            except Exception as e:
                print(f"ERROR: Unexpected SSH error: {type(e).__name__}: {e}\n{traceback.format_exc()}")
                return SSHResult(
                    stdout="",
                    stderr=f"Unexpected SSH error: {e}",
                    exit_status=-1,
                    success=False,
                    command=command
                )
            finally:
                if ssh_client:
                    ssh_client.close()
                    print("DEBUG: SSH connection closed.")

        print("DEBUG: Scheduling SSH command...")
        result = await loop.run_in_executor(None, run_ssh)
        print("DEBUG: SSH command result received.")
        return result
